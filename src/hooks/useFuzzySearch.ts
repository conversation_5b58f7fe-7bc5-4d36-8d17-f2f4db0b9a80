import { useState, useEffect, useMemo } from 'react';

interface FuzzySearchOptions {
  threshold?: number;
  includeScore?: boolean;
  keys?: string[];
  debounceMs?: number;
}

interface FuzzyMatch<T> {
  item: T;
  score: number;
  matches: Array<{
    key: string;
    indices: number[];
    value: string;
  }>;
}

const findIndices = (text: string, pattern: string): number[] => {
  const indices: number[] = [];
  let startIndex = 0;
  let index = text.indexOf(pattern, startIndex);
  
  while (index !== -1) {
    for (let i = 0; i < pattern.length; i++) {
      indices.push(index + i);
    }
    startIndex = index + 1;
    index = text.indexOf(pattern, startIndex);
  }
  
  return indices;
};

export function useFuzzySearch<T>(
  data: T[],
  searchTerm: string,
  options: FuzzySearchOptions = {}
) {
  const [debouncedTerm, setDebouncedTerm] = useState(searchTerm);

  const {
    threshold = 0.6,
    includeScore = true,
    keys = ['name'],
    debounceMs = 300
  } = options;

  // Debounce search term
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedTerm(searchTerm);
    }, debounceMs);

    return () => clearTimeout(timer);
  }, [searchTerm, debounceMs]);

  const fuzzyMatch = (text: string, pattern: string): number => {
    if (!pattern) return 1;
    if (!text) return 0;

    const textLower = text.toLowerCase();
    const patternLower = pattern.toLowerCase();

    // Exact match gets highest score
    if (textLower === patternLower) return 1;

    // Starts with pattern gets high score
    if (textLower.startsWith(patternLower)) return 0.9;

    // Contains pattern gets medium score
    if (textLower.includes(patternLower)) return 0.7;

    // Fuzzy matching using Levenshtein distance
    const distance = levenshteinDistance(textLower, patternLower);
    const maxLength = Math.max(textLower.length, patternLower.length);
    return maxLength === 0 ? 0 : (maxLength - distance) / maxLength;
  };

  const levenshteinDistance = (str1: string, str2: string): number => {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));

    for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
    for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;

    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1,
          matrix[j - 1][i] + 1,
          matrix[j - 1][i - 1] + indicator
        );
      }
    }

    return matrix[str2.length][str1.length];
  };

  const getNestedValue = (obj: any, key: string): string => {
    return key.split('.').reduce((current, k) => current?.[k], obj) || '';
  };

  const results = useMemo(() => {
    if (!debouncedTerm.trim()) {
      return data.map(item => ({
        item,
        score: 1,
        matches: []
      }));
    }

    const matches: FuzzyMatch<T>[] = [];

    for (const item of data) {
      let bestScore = 0;
      const itemMatches: Array<{
        key: string;
        indices: number[];
        value: string;
      }> = [];

      for (const key of keys) {
        const value = getNestedValue(item, key);
        if (!value) continue;

        const score = fuzzyMatch(value, debouncedTerm);
        
        if (score > bestScore) {
          bestScore = score;
        }

        if (score >= threshold) {
          const indices = findIndices(value.toLowerCase(), debouncedTerm.toLowerCase());
          itemMatches.push({
            key,
            indices,
            value
          });
        }
      }

      if (bestScore >= threshold) {
        matches.push({
          item,
          score: bestScore,
          matches: itemMatches
        });
      }
    }

    // Sort by score (highest first)
    return matches.sort((a, b) => b.score - a.score);
  }, [data, debouncedTerm, threshold, keys]);

  return {
    results: results.map(r => r.item),
    matches: results,
    isSearching: searchTerm !== debouncedTerm,
    searchTerm: debouncedTerm
  };
}
