import React from 'react';
import { ProcessedData } from '../types';
import { LoadingSpinner } from './LoadingSpinner';
import { AlertTriangle, RefreshCw } from 'lucide-react';

interface DataWrapperProps {
  data: ProcessedData & { isLoading: boolean; error: string | null };
  children: React.ReactNode;
}

export const DataWrapper: React.FC<DataWrapperProps> = ({ data, children }) => {
  if (data.isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <LoadingSpinner size="lg" text="Loading class data..." />
      </div>
    );
  }

  if (data.error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-8 max-w-md w-full">
          <div className="flex items-center mb-4">
            <div className="flex-shrink-0">
              <AlertTriangle className="h-8 w-8 text-red-500" />
            </div>
            <div className="ml-3">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                Error Loading Data
              </h3>
            </div>
          </div>
          
          <div className="mb-4">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {data.error}
            </p>
          </div>

          <button
            onClick={() => window.location.reload()}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Reload Page
          </button>
        </div>
      </div>
    );
  }

  return <>{children}</>;
};
