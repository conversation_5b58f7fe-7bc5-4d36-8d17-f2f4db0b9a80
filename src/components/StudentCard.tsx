import React from 'react';
import { Student } from '../types';
import { GraduationCap, Mail } from 'lucide-react';
import { normalizeName } from '../data/nameOverrides';

interface StudentCardProps {
  student: Student;
  classCount: number;
  onClick: () => void;
}

export const StudentCard: React.FC<StudentCardProps> = ({
  student,
  classCount,
  onClick
}) => {
  return (
    <div
      onClick={onClick}
      className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 hover:shadow-md hover:border-purple-300 dark:hover:border-purple-600 transition-all duration-200 cursor-pointer"
    >
      <div className="flex items-start justify-between mb-3">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          {normalizeName(student.name)}
        </h3>
        <span className="inline-block px-2 py-1 bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300 text-xs rounded-full">
          Class of {student.gradYear}
        </span>
      </div>

      <div className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
        {student.email && (
          <div className="flex items-center gap-2">
            <Mail className="w-4 h-4" />
            <span className="truncate">{student.email}</span>
          </div>
        )}
        <div className="flex items-center gap-2">
          <GraduationCap className="w-4 h-4" />
          <span>{classCount} classes enrolled</span>
        </div>
      </div>
    </div>
  );
};
