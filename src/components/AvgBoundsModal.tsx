import React, { useState, useEffect } from 'react';

interface AvgBoundsModalProps {
  open: boolean;
  min: number;
  max: number;
  onClose: () => void;
  onSave: (min: number, max: number) => void;
}

export const AvgBoundsModal: React.FC<AvgBoundsModalProps> = ({ open, min, max, onClose, onSave }) => {
  const [localMin, setLocalMin] = useState<string>(String(min));
  const [localMax, setLocalMax] = useState<string>(String(max));

  useEffect(() => {
    setLocalMin(String(min));
    setLocalMax(String(max));
  }, [min, max]);

  if (!open) return null;

  const save = () => {
    const parsedMin = Math.max(0, parseInt(localMin || '0', 10) || 0);
    const parsedMax = Math.max(parsedMin, parseInt(localMax || String(parsedMin), 10) || parsedMin);
    onSave(parsedMin, parsedMax);
    onClose();
  };

  const resetToDefaults = () => {
    const defaultMin = 3;
    const defaultMax = 25;
    setLocalMin(String(defaultMin));
    setLocalMax(String(defaultMax));
    onSave(defaultMin, defaultMax);
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 dark:bg-black/80 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-md p-6">
        <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">Average class size bounds</h3>
        <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">Only classes with size between the bounds (inclusive) will be counted toward the average.</p>

        <div className="grid grid-cols-2 gap-4 mb-4">
          <div>
            <label className="block text-sm text-gray-700 dark:text-gray-300 mb-1">Minimum students</label>
            <input
              type="number"
              min={0}
              value={localMin}
              onChange={(e) => setLocalMin(e.target.value)}
              className="w-full border border-gray-200 dark:border-gray-700 rounded px-3 py-2 bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
            />
          </div>
          <div>
            <label className="block text-sm text-gray-700 dark:text-gray-300 mb-1">Maximum students</label>
            <input
              type="number"
              min={0}
              value={localMax}
              onChange={(e) => setLocalMax(e.target.value)}
              className="w-full border border-gray-200 dark:border-gray-700 rounded px-3 py-2 bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
            />
          </div>
        </div>

        <div className="flex justify-end gap-2">
          <button onClick={onClose} className="px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white rounded">Cancel</button>
          <button onClick={resetToDefaults} className="px-4 py-2 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300 rounded">Reset</button>
          <button onClick={save} className="px-4 py-2 bg-blue-600 dark:bg-blue-500 text-white rounded">Save</button>
        </div>
      </div>
    </div>
  );
};

export default AvgBoundsModal;
