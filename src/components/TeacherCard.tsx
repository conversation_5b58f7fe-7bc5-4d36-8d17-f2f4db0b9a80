import React from 'react';
import { Faculty } from '../types';
import { BookOpen, Mail } from 'lucide-react';

interface TeacherCardProps {
  teacher: Faculty;
  classCount: number;
  studentCount: number;
  onClick: () => void;
}

export const TeacherCard: React.FC<TeacherCardProps> = ({ 
  teacher, 
  classCount, 
  studentCount,
  onClick 
}) => {
  return (
    <div
      onClick={onClick}
      className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 hover:shadow-md hover:border-green-300 dark:hover:border-green-600 transition-all duration-200 cursor-pointer"
    >
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
        {teacher.name}
      </h3>
      
      <div className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
        {teacher.email && (
          <div className="flex items-center gap-2">
            <Mail className="w-4 h-4" />
            <span className="truncate">{teacher.email}</span>
          </div>
        )}
        <div className="flex items-center gap-2">
          <BookOpen className="w-4 h-4" />
          <span>{classCount} classes, {studentCount} total students</span>
        </div>
      </div>
    </div>
  );
};