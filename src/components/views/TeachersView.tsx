import React, { useState, useMemo } from 'react';
import { ProcessedData } from '../../types';
import { SearchBar } from '../SearchBar';
import { FilterDropdown } from '../FilterDropdown';
import { TeacherCard } from '../TeacherCard';
import { TeacherDetailModal } from '../TeacherDetailModal';
import { Users } from 'lucide-react';

interface TeachersViewProps {
  data: ProcessedData;
}

export const TeachersView: React.FC<TeachersViewProps> = ({ data }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTeacher, setSelectedTeacher] = useState<any>(null);
  const [sortBy, setSortBy] = useState('name_asc');

  const teachersWithClasses = useMemo(() => {
    return data.faculty
      .filter(teacher => data.teacherClassMap.has(teacher.email || ''))
      .map(teacher => {
        const classes = data.teacherClassMap.get(teacher.email || '') || [];
        const studentCount = classes.reduce((sum, cls) => sum + cls.students.length, 0);
        return {
          teacher,
          classCount: classes.length,
          studentCount,
        };
      });
  }, [data.faculty, data.teacherClassMap]);

  const filteredTeachers = useMemo(() => {
    return teachersWithClasses.filter(({ teacher }) =>
      teacher.name.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [teachersWithClasses, searchTerm]);

  const sortOptions = [
    { value: 'name_asc', label: 'Name (A → Z)' },
    { value: 'name_desc', label: 'Name (Z → A)' },
    { value: 'students_desc', label: 'Students (most)' },
    { value: 'students_asc', label: 'Students (fewest)' },
  ];

  const sortedTeachers = useMemo(() => {
    const list = [...filteredTeachers];
    switch (sortBy) {
      case 'name_desc':
        return list.sort((a, b) => b.teacher.name.localeCompare(a.teacher.name));
      case 'students_desc':
        return list.sort((a, b) => b.studentCount - a.studentCount);
      case 'students_asc':
        return list.sort((a, b) => a.studentCount - b.studentCount);
      case 'name_asc':
      default:
        return list.sort((a, b) => a.teacher.name.localeCompare(b.teacher.name));
    }
  }, [filteredTeachers, sortBy]);

  return (
    <>
      <div className="space-y-6">
          <div>
            <SearchBar
              value={searchTerm}
              onChange={setSearchTerm}
              placeholder="Search teachers..."
            />
            <div className="mt-3 flex flex-col lg:flex-row gap-4">
              <div className="w-full lg:w-48">
                <FilterDropdown
                  label="Sort"
                  value={sortBy}
                  onChange={setSortBy}
                  options={sortOptions}
                />
              </div>
            </div>
          </div>

        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold text-gray-900">
            Teachers ({sortedTeachers.length})
          </h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {sortedTeachers.map(({ teacher, classCount, studentCount }, index) => (
            <TeacherCard
              key={index}
              teacher={teacher}
              classCount={classCount}
              studentCount={studentCount}
              onClick={() => setSelectedTeacher(teacher)}
            />
          ))}
        </div>

        {filteredTeachers.length === 0 && (
          <div className="text-center py-12">
            <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">No teachers found matching your search.</p>
          </div>
        )}
      </div>

      {selectedTeacher && (
        <TeacherDetailModal
          teacher={selectedTeacher}
          classes={data.teacherClassMap.get(selectedTeacher.email || '') || []}
          data={data}
          onClose={() => setSelectedTeacher(null)}
        />
      )}
    </>
  );
};
