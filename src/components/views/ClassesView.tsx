import React, { useState, useMemo } from 'react';
import { ProcessedData } from '../../types';
import { SearchBar } from '../SearchBar';
import { FilterDropdown } from '../FilterDropdown';
import { ClassCard } from '../ClassCard';
import { ClassDetailModal } from '../ClassDetailModal';
import { BookOpen } from 'lucide-react';

interface ClassesViewProps {
  data: ProcessedData;
}

export const ClassesView: React.FC<ClassesViewProps> = ({ data }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTeacher, setSelectedTeacher] = useState('all');
  const [selectedBlock, setSelectedBlock] = useState('all');
  const [selectedClass, setSelectedClass] = useState<any>(null);
  const [sortBy, setSortBy] = useState('name_asc');

  const teacherOptions = useMemo(() => [
    { value: 'all', label: 'All Teachers' },
    ...data.faculty
      .filter(teacher => data.teacherClassMap.has(teacher.email || ''))
      .map(teacher => ({
        value: teacher.email || '',
        label: teacher.name,
      }))
      .sort((a, b) => a.label.localeCompare(b.label)),
  ], [data.faculty, data.teacherClassMap]);

  // Remove block grouping and variants, just get unique blocks
  const blockOptions = useMemo(() => [
    { value: 'all', label: 'All Blocks' },
    ...Array.from(new Set(data.classes.map(cls => cls.block)))
      .sort((a, b) => a.localeCompare(b, undefined, { numeric: true }))
      .map(b => ({ value: b, label: `Block ${b}` }))
  ], [data.classes]);

  const filteredClasses = useMemo(() => {
    return data.classes.filter(cls => {
      const matchesSearch =
        cls.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        cls.teacher.name.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesTeacher = selectedTeacher === 'all' || cls.teacher.email === selectedTeacher;
      const matchesBlock = selectedBlock === 'all' || cls.block === selectedBlock;

      return matchesSearch && matchesTeacher && matchesBlock;
    });
  }, [data.classes, searchTerm, selectedTeacher, selectedBlock]);

  const sortOptions = [
    { value: 'name_asc', label: 'Name (A → Z)' },
    { value: 'name_desc', label: 'Name (Z → A)' },
    { value: 'size_desc', label: 'Size (largest)' },
    { value: 'size_asc', label: 'Size (smallest)' },
  ];

  const sortedClasses = useMemo(() => {
    const list = [...filteredClasses];
    switch (sortBy) {
      case 'name_desc':
        return list.sort((a, b) => b.name.localeCompare(a.name));
      case 'size_desc':
        return list.sort((a, b) => b.students.length - a.students.length);
      case 'size_asc':
        return list.sort((a, b) => a.students.length - b.students.length);
      case 'name_asc':
      default:
        return list.sort((a, b) => a.name.localeCompare(b.name));
    }
  }, [filteredClasses, sortBy]);

  return (
    <>
      <div className="space-y-6">
        <div>
          <SearchBar
            value={searchTerm}
            onChange={setSearchTerm}
            placeholder="Search classes or teachers..."
          />
          <div className="mt-3 flex flex-col lg:flex-row gap-4">
            <div className="w-full lg:w-48">
              <FilterDropdown
                label="Teacher"
                value={selectedTeacher}
                onChange={setSelectedTeacher}
                options={teacherOptions}
              />
            </div>
            <div className="w-full lg:w-48">
              <FilterDropdown
                label="Block"
                value={selectedBlock}
                onChange={setSelectedBlock}
                options={blockOptions}
              />
            </div>
            <div className="w-full lg:w-48">
              <FilterDropdown
                label="Sort"
                value={sortBy}
                onChange={setSortBy}
                options={sortOptions}
              />
            </div>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold text-gray-900">
            Classes ({filteredClasses.length})
          </h2>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
          {sortedClasses.map((cls, index) => (
            <ClassCard
              key={index}
              classInfo={cls}
              onClick={() => setSelectedClass(cls)}
            />
          ))}
        </div>

        {filteredClasses.length === 0 && (
          <div className="text-center py-12">
            <BookOpen className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">No classes found matching your criteria.</p>
          </div>
        )}
      </div>

      {selectedClass && (
        <ClassDetailModal
          classInfo={selectedClass}
          data={data}
          onClose={() => setSelectedClass(null)}
        />
      )}
    </>
  );
};
