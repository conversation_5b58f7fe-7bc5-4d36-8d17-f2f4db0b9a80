import React, { useState, useMemo } from 'react';
import { ProcessedData, ClassInfo, Student, Faculty } from '../../types';
import { SearchBar } from '../SearchBar';
import { FilterDropdown } from '../FilterDropdown';
import { ClassDetailModal } from '../ClassDetailModal';
import { StudentDetailModal } from '../StudentDetailModal';
import { TeacherDetailModal } from '../TeacherDetailModal';
import { Search } from 'lucide-react';
import { normalizeName } from '../../data/nameOverrides';
import { useFuzzySearch } from '../../hooks/useFuzzySearch';

interface SearchViewProps {
  data: ProcessedData;
}

export const SearchView: React.FC<SearchViewProps> = ({ data }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('relevance');
  const [selectedClass, setSelectedClass] = useState<ClassInfo | null>(null);
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null);
  const [selectedTeacher, setSelectedTeacher] = useState<Faculty | null>(null);

  // Unified search data
  const unifiedData = useMemo(() => [
    ...data.classes.map(cls => ({ ...cls, type: 'class' })),
    ...data.students.map(stu => ({ ...stu, type: 'student' })),
    ...data.faculty.map(teach => ({ ...teach, type: 'teacher' })),
  ], [data]);

  // Unified fuzzy search
  const unifiedResults = useFuzzySearch(unifiedData, searchTerm, {
    keys: [
      'name',
      'teacher.name',
      'block',
      'gradYear',
      'email',
    ],
    threshold: 0.3
  });

  const sortOptions = [
    { value: 'relevance', label: 'Relevance' },
    { value: 'name_asc', label: 'Name (A → Z)' },
    { value: 'name_desc', label: 'Name (Z → A)' },
    { value: 'size_desc', label: 'Size (largest)' },
    { value: 'size_asc', label: 'Size (smallest)' },
  ];

  const applySortToList = (list: any[]) => {
    const copied = [...list];
    switch (sortBy) {
      case 'name_asc':
        return copied.sort((a, b) => (a.name || a.teacher?.name || '').localeCompare(b.name || b.teacher?.name || ''));
      case 'name_desc':
        return copied.sort((a, b) => (b.name || b.teacher?.name || '').localeCompare(a.name || a.teacher?.name || ''));
      case 'size_desc':
        return copied.sort((a, b) => (b.students?.length || 0) - (a.students?.length || 0));
      case 'size_asc':
        return copied.sort((a, b) => (a.students?.length || 0) - (b.students?.length || 0));
      case 'relevance':
      default:
        return copied;
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <SearchBar
          value={searchTerm}
          onChange={setSearchTerm}
          placeholder="Search across all classes, students, and teachers..."
          isLoading={unifiedResults.isSearching}
        />
        <div className="mt-3 flex flex-col lg:flex-row gap-4">
          <div className="w-full lg:w-48">
            <FilterDropdown
              label="Sort"
              value={sortBy}
              onChange={setSortBy}
              options={sortOptions}
            />
          </div>
        </div>
      </div>

      {searchTerm ? (
        <div className="space-y-4">
          {applySortToList(unifiedResults.results).length === 0 && (
            <div className="text-center py-12">
              <Search className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 dark:text-gray-400">No results found for "{searchTerm}".</p>
            </div>
          )}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {applySortToList(unifiedResults.results).map((item, index) => {
              if (item.type === 'class') {
                return (
                  <div
                    key={index}
                    onClick={() => setSelectedClass(item as ClassInfo)}
                    className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 hover:shadow-md hover:border-blue-300 dark:hover:border-blue-600 transition-all duration-200 cursor-pointer"
                  >
                    <h4 className="font-semibold text-gray-900 dark:text-white mb-2">{item.name}</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">Teacher: {item.teacher.name}</p>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">Block: {item.block}</p>
                    <p className="text-sm text-blue-600 dark:text-blue-400">{item.students.length} students</p>
                  </div>
                );
              } else if (item.type === 'student') {
                return (
                  <div
                    key={index}
                    onClick={() => setSelectedStudent(item as Student)}
                    className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 hover:shadow-md hover:border-purple-300 dark:hover:border-purple-600 transition-all duration-200 cursor-pointer"
                  >
                    <h4 className="font-semibold text-gray-900 dark:text-white mb-2">{normalizeName(item.name)}</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">Class of {item.gradYear}</p>
                    {item.email && (
                      <p className="text-xs text-gray-500 dark:text-gray-400 truncate">{item.email}</p>
                    )}
                  </div>
                );
              } else if (item.type === 'teacher') {
                const teacherClasses = data.teacherClassMap.get(item.email || '') || [];
                return (
                  <div
                    key={index}
                    onClick={() => setSelectedTeacher(item as Faculty)}
                    className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 hover:shadow-md hover:border-green-300 dark:hover:border-green-600 transition-all duration-200 cursor-pointer"
                  >
                    <h4 className="font-semibold text-gray-900 dark:text-white mb-2">{item.name}</h4>
                    {item.email && (
                      <p className="text-xs text-gray-500 dark:text-gray-400 mb-2 truncate">{item.email}</p>
                    )}
                    <p className="text-sm text-green-600 dark:text-green-400">{teacherClasses.length} classes</p>
                  </div>
                );
              }
              return null;
            })}
          </div>
        </div>
      ) : (
        <div className="text-center py-12">
          <Search className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500 dark:text-gray-400">Start typing to search across all classes, students, and teachers.</p>
        </div>
      )}

      {/* Modals */}
      {selectedClass && (
        <ClassDetailModal
          classInfo={selectedClass}
          data={data}
          onClose={() => setSelectedClass(null)}
        />
      )}

      {selectedStudent && (
        <StudentDetailModal
          student={selectedStudent}
          classes={data.studentClassMap.get(selectedStudent.email || normalizeName(selectedStudent.name)) || []}
          data={data}
          onClose={() => setSelectedStudent(null)}
        />
      )}

      {selectedTeacher && (
        <TeacherDetailModal
          teacher={selectedTeacher}
          classes={data.teacherClassMap.get(selectedTeacher.email || '') || []}
          data={data}
          onClose={() => setSelectedTeacher(null)}
        />
      )}
    </div>
  );
};
