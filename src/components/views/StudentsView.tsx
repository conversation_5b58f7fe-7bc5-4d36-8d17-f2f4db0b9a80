import React, { useState, useMemo } from 'react';
import { ProcessedData } from '../../types';
import { SearchBar } from '../SearchBar';
import { FilterDropdown } from '../FilterDropdown';
import { StudentCard } from '../StudentCard';
import { StudentDetailModal } from '../StudentDetailModal';
import { GraduationCap } from 'lucide-react';
import { normalizeName } from '../../data/nameOverrides';

interface StudentsViewProps {
  data: ProcessedData;
}

export const StudentsView: React.FC<StudentsViewProps> = ({ data }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedGradYear, setSelectedGradYear] = useState('all');
  const [sortBy, setSortBy] = useState('name_asc');
  const [selectedStudent, setSelectedStudent] = useState<any>(null);

  const gradYearOptions = useMemo(() => {
    const gradYears = new Set(data.students.map(student => student.gradYear));
    return [
      { value: 'all', label: 'All Graduation Years' },
      ...Array.from(gradYears)
        .sort()
        .map(year => ({ value: year, label: `Class of ${year}` })),
    ];
  }, [data.students]);

  const filteredStudents = useMemo(() => {
    return data.students.filter(student => {
      const matchesSearch = normalizeName(student.name).toLowerCase().includes(searchTerm.toLowerCase());
      const matchesGradYear = selectedGradYear === 'all' || student.gradYear === selectedGradYear;

      return matchesSearch && matchesGradYear;
    });
  }, [data.students, searchTerm, selectedGradYear]);

  const sortOptions = [
    { value: 'name_asc', label: 'Name (A → Z)' },
    { value: 'name_desc', label: 'Name (Z → A)' },
    { value: 'size_desc', label: 'Class size (largest)' },
    { value: 'size_asc', label: 'Class size (smallest)' },
  ];

  const sortedStudents = useMemo(() => {
    const list = [...filteredStudents];
    switch (sortBy) {
      case 'name_desc':
        return list.sort((a, b) => normalizeName(b.name).localeCompare(normalizeName(a.name)));
      case 'size_desc':
        return list.sort((a, b) => (data.studentClassMap.get(b.email || normalizeName(b.name))?.length || 0) - (data.studentClassMap.get(a.email || normalizeName(a.name))?.length || 0));
      case 'size_asc':
        return list.sort((a, b) => (data.studentClassMap.get(a.email || normalizeName(a.name))?.length || 0) - (data.studentClassMap.get(b.email || normalizeName(b.name))?.length || 0));
      case 'name_asc':
      default:
        return list.sort((a, b) => normalizeName(a.name).localeCompare(normalizeName(b.name)));
    }
  }, [filteredStudents, sortBy, data.studentClassMap]);

  const getStudentClassCount = (student: any) => {
    const studentKey = student.email || normalizeName(student.name);
    return data.studentClassMap.get(studentKey)?.length || 0;
  };

  return (
    <>
      <div className="space-y-6">
        <div>
          <SearchBar
            value={searchTerm}
            onChange={setSearchTerm}
            placeholder="Search students..."
          />
          <div className="mt-3 flex flex-col lg:flex-row gap-4">
            <div className="w-full lg:w-48">
              <FilterDropdown
                label="Graduation Year"
                value={selectedGradYear}
                onChange={setSelectedGradYear}
                options={gradYearOptions}
              />
            </div>
            <div className="w-full lg:w-48">
              <FilterDropdown
                label="Sort"
                value={sortBy}
                onChange={setSortBy}
                options={sortOptions}
              />
            </div>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold text-gray-900">
            Students ({sortedStudents.length})
          </h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {sortedStudents.map((student, index) => (
            <StudentCard
              key={index}
              student={student}
              classCount={getStudentClassCount(student)}
              onClick={() => setSelectedStudent(student)}
            />
          ))}
        </div>

        {filteredStudents.length === 0 && (
          <div className="text-center py-12">
            <GraduationCap className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">No students found matching your criteria.</p>
          </div>
        )}
      </div>

      {selectedStudent && (
        <StudentDetailModal
          student={selectedStudent}
          classes={data.studentClassMap.get(selectedStudent.email || normalizeName(selectedStudent.name)) || []}
          data={data}
          onClose={() => setSelectedStudent(null)}
        />
      )}
    </>
  );
};
