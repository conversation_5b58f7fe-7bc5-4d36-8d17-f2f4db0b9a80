import React from 'react';
import { ClassInfo } from '../types';
import { Users, Clock } from 'lucide-react';
import { normalizeName } from '../data/nameOverrides';

interface ClassCardProps {
  classInfo: ClassInfo;
  onClick: () => void;
}

export const ClassCard: React.FC<ClassCardProps> = ({ classInfo, onClick }) => {
  return (
    <div
      onClick={onClick}
      className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md hover:border-blue-300 dark:hover:border-blue-600 transition-all duration-200 cursor-pointer"
    >
      <div className="mb-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
          {classInfo.name}
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">
          Teacher: {classInfo.teacher.name}
        </p>
        <div className="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
          <div className="flex items-center gap-1">
            <Clock className="w-4 h-4" />
            <span>{classInfo.block}</span>
          </div>
          <div className="flex items-center gap-1">
            <Users className="w-4 h-4" />
            <span>{classInfo.students.length} students</span>
          </div>
        </div>
      </div>
      <div className="flex flex-wrap gap-1">
        {classInfo.students.slice(0, 3).map((student, index) => (
          <span
            key={index}
            className="inline-block px-2 py-1 bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 text-xs rounded-full"
          >
            {normalizeName(student.name)}
          </span>
        ))}
        {classInfo.students.length > 3 && (
          <span className="inline-block px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 text-xs rounded-full">
            +{classInfo.students.length - 3} more
          </span>
        )}
      </div>
    </div>
  );
};
