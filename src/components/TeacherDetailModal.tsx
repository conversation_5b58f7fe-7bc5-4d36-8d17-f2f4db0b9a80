import React, { useState } from 'react';
import { Faculty, ClassInfo, Student, ProcessedData } from '../types';
import { X, Users, Mail, BookOpen, Clock } from 'lucide-react';
import { normalizeName } from '../data/nameOverrides';
import { ClassDetailModal } from './ClassDetailModal';
import { StudentDetailModal } from './StudentDetailModal';
import { Modal } from './Modal';

interface TeacherDetailModalProps {
  teacher: Faculty;
  classes: ClassInfo[];
  data: ProcessedData;
  onClose: () => void;
}

export const TeacherDetailModal: React.FC<TeacherDetailModalProps> = ({
  teacher,
  classes,
  data,
  onClose
}) => {
  const [expandedClasses, setExpandedClasses] = useState<Set<string>>(new Set());
  const [selectedClass, setSelectedClass] = useState<ClassInfo | null>(null);
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null);

  const toggleExpanded = (key: string) => {
    setExpandedClasses(prev => {
      const next = new Set(prev);
      if (next.has(key)) next.delete(key);
      else next.add(key);
      return next;
    });
  };
  const totalStudents = classes.reduce((sum, cls) => sum + cls.students.length, 0);
  const classesByBlock = classes.reduce((acc, cls) => {
    if (!acc[cls.block]) acc[cls.block] = [];
    acc[cls.block].push(cls);
    return acc;
  }, {} as Record<string, ClassInfo[]>);

  return (
    <Modal isOpen={true} onClose={onClose}>
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="sticky top-0 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4 flex items-center justify-between">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">{teacher.name}</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <div className="p-6">
          <div className="mb-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div className="flex items-center gap-2">
                <BookOpen className="w-4 h-4 text-green-600" />
                <span className="text-sm text-gray-600 dark:text-gray-400">{classes.length} Classes</span>
              </div>
              <div className="flex items-center gap-2">
                <Users className="w-4 h-4 text-green-600" />
                <span className="text-sm text-gray-600 dark:text-gray-400">{totalStudents} Total Students</span>
              </div>
            </div>

            {teacher.email && (
              <div className="flex items-center gap-2">
                <Mail className="w-4 h-4 text-gray-400" />
                <span className="text-sm text-gray-500 dark:text-gray-400">{teacher.email}</span>
              </div>
            )}
          </div>

          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Teaching Schedule</h3>
            {Object.keys(classesByBlock).length > 0 ? (
              <div className="space-y-4">
                {Object.entries(classesByBlock)
                  .sort(([a], [b]) => a.localeCompare(b))
                  .map(([block, blockClasses]) => (
                    <div key={block}>
                      <div className="flex items-center gap-2 mb-2">
                        <Clock className="w-4 h-4 text-green-600" />
                        <h4 className="font-medium text-gray-900 dark:text-white">Block {block}</h4>
                      </div>
                      <div className="grid grid-cols-1 gap-2 ml-6">
                        {blockClasses.map((cls, index) => {
                          const classKey = `${block}-${cls.name}`;
                          return (
                            <div key={index} className="p-3 bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-700 rounded-lg">
                              <p
                                className="font-medium text-green-900 dark:text-green-200 cursor-pointer hover:text-green-700 dark:hover:text-green-300 transition-colors"
                                onClick={() => setSelectedClass(cls)}
                              >
                                {cls.name}
                              </p>
                              <p className="text-sm text-green-700 dark:text-green-400">{cls.students.length} students enrolled</p>
                              <div className="mt-2 flex flex-wrap gap-1">
                                {expandedClasses.has(classKey)
                                  ? cls.students.map((student, idx) => (
                                      <span
                                        key={idx}
                                        className="inline-block px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 text-xs rounded cursor-pointer hover:bg-green-200 dark:hover:bg-green-800 transition-colors"
                                        onClick={() => setSelectedStudent(student)}
                                      >
                                        {normalizeName(student.name)}
                                      </span>
                                    ))
                                  : cls.students.slice(0, 5).map((student, idx) => (
                                      <span
                                        key={idx}
                                        className="inline-block px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 text-xs rounded cursor-pointer hover:bg-green-200 dark:hover:bg-green-800 transition-colors"
                                        onClick={() => setSelectedStudent(student)}
                                      >
                                        {normalizeName(student.name)}
                                      </span>
                                    ))}

                                {cls.students.length > 5 && (
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      toggleExpanded(classKey);
                                    }}
                                    className="inline-block px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 text-xs rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                                  >
                                    {expandedClasses.has(classKey) ? 'Show less' : `+${cls.students.length - 5} more`}
                                  </button>
                                )}
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <BookOpen className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                <p className="text-gray-500 dark:text-gray-400">No classes found for this teacher.</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {selectedClass && (
        <ClassDetailModal
          classInfo={selectedClass}
          data={data}
          onClose={() => setSelectedClass(null)}
        />
      )}

      {selectedStudent && (
        <StudentDetailModal
          student={selectedStudent}
          classes={data.studentClassMap.get(selectedStudent.email || normalizeName(selectedStudent.name)) || []}
          data={data}
          onClose={() => setSelectedStudent(null)}
        />
      )}
    </Modal>
  );
};
