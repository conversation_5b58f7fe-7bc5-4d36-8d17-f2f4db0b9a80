import React from 'react';
import { ChevronDown } from 'lucide-react';

interface SimpleOption { value: string; label: string }
interface OptGroup { label: string; options: SimpleOption[] }

type OptionsType = Array<SimpleOption> | Array<OptGroup>;

interface FilterDropdownProps {
  label: string;
  value: string;
  onChange: (value: string) => void;
  // supports either flat options or grouped optgroups
  options: OptionsType;
}

export const FilterDropdown: React.FC<FilterDropdownProps> = ({
  label,
  value,
  onChange,
  options,
}) => {
  return (
    <div className="relative">
      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
        {label}
      </label>
      <div className="relative">
        <select
          value={value}
          onChange={(e) => onChange(e.target.value)}
          className="w-full pl-3 pr-10 py-2 border border-gray-200 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
        >
          {/* detect if options is grouped by checking first item */}
          {Array.isArray(options) && options.length > 0 && 'options' in (options[0] as any)
            ? // render optgroups
              (options as Array<OptGroup>).map((group) => (
                <optgroup key={group.label} label={group.label}>
                  {group.options.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </optgroup>
              ))
            : // render flat options
              (options as Array<SimpleOption>).map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
        </select>
        <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 w-4 h-4 pointer-events-none" />
      </div>
    </div>
  );
};