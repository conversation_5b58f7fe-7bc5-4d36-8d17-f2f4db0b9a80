import React, { useState } from 'react';
import { Student, ClassInfo, ProcessedData } from '../types';
import { X, GraduationCap, Mail, BookOpen, Clock } from 'lucide-react';
import { normalizeName } from '../data/nameOverrides';
import { ClassDetailModal } from './ClassDetailModal';
import { Modal } from './Modal';

interface StudentDetailModalProps {
  student: Student;
  classes: ClassInfo[];
  data: ProcessedData;
  onClose: () => void;
}

export const StudentDetailModal: React.FC<StudentDetailModalProps> = ({
  student,
  classes,
  data,
  onClose
}) => {
  const [selectedClass, setSelectedClass] = useState<ClassInfo | null>(null);

  const classesByBlock = classes.reduce((acc, cls) => {
    if (!acc[cls.block]) acc[cls.block] = [];
    acc[cls.block].push(cls);
    return acc;
  }, {} as Record<string, ClassInfo[]>);

  return (
    <Modal isOpen={true} onClose={onClose}>
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="sticky top-0 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4 flex items-center justify-between">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">{normalizeName(student.name)}</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <div className="p-6">
          <div className="mb-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div className="flex items-center gap-2">
                <GraduationCap className="w-4 h-4 text-purple-600" />
                <span className="text-sm text-gray-600 dark:text-gray-400">Class of {student.gradYear}</span>
              </div>
              <div className="flex items-center gap-2">
                <BookOpen className="w-4 h-4 text-purple-600" />
                <span className="text-sm text-gray-600 dark:text-gray-400">{classes.length} Classes Enrolled</span>
              </div>
            </div>

            {student.email && (
              <div className="flex items-center gap-2">
                <Mail className="w-4 h-4 text-gray-400" />
                <span className="text-sm text-gray-500 dark:text-gray-400">{student.email}</span>
              </div>
            )}
          </div>

          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Class Schedule</h3>
            {Object.keys(classesByBlock).length > 0 ? (
              <div className="space-y-4">
                {Object.entries(classesByBlock)
                  .sort(([a], [b]) => a.localeCompare(b))
                  .map(([block, blockClasses]) => (
                    <div key={block}>
                      <div className="flex items-center gap-2 mb-2">
                        <Clock className="w-4 h-4 text-blue-600" />
                        <h4 className="font-medium text-gray-900 dark:text-white">Block {block}</h4>
                      </div>
                      <div className="grid grid-cols-1 gap-2 ml-6">
                        {blockClasses.map((cls, index) => (
                          <div key={index} className="p-3 bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-700 rounded-lg">
                            <p
                              className="font-medium text-blue-900 dark:text-blue-200 cursor-pointer hover:text-blue-700 dark:hover:text-blue-300 transition-colors"
                              onClick={() => setSelectedClass(cls)}
                            >
                              {cls.name}
                            </p>
                            <p className="text-sm text-blue-700 dark:text-blue-400">Teacher: {cls.teacher.name}</p>
                            <p className="text-sm text-blue-600 dark:text-blue-300">{cls.students.length} students total</p>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <BookOpen className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                <p className="text-gray-500 dark:text-gray-400">No classes found for this student.</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {selectedClass && (
        <ClassDetailModal
          classInfo={selectedClass}
          data={data}
          onClose={() => setSelectedClass(null)}
        />
      )}
    </Modal>
  );
};
