@tailwind base;
@tailwind components;
@tailwind utilities;

/* Prevent white flash on scroll by setting body background */
html, body {
  @apply bg-gray-50 dark:bg-gray-900;
  overscroll-behavior: none;
}

/* Ensure modal backdrops cover full viewport */
.modal-backdrop {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
}

/* Custom scrollbar styles */
.scrollbar-hide {
  -ms-overflow-style: none;  /* Internet Explorer 10+ */
  scrollbar-width: none;  /* Firefox */
}
.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Safari and Chrome */
}

/* Smooth transitions for dark mode */
* {
  transition-property: color, background-color, border-color;
  transition-duration: 150ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Mobile-friendly touch targets */
@media (max-width: 640px) {
  button, [role="button"] {
    min-height: 44px;
    min-width: 44px;
  }
}

/* Focus styles for accessibility */
.focus-visible:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}
