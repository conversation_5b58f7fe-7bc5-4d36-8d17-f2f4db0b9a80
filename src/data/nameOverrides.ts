// nameOverrides.ts
// A small central list of name overrides for canonicalization.
// Add entries here when you want a display name (or incoming name)
// to be converted to another name.

export const nameOverrides: Record<string, string> = {
  // Example: shorter / nickname -> canonical full name
  "<PERSON><PERSON>": "<PERSON>",
  "<PERSON>": "<PERSON>",
  "<PERSON><PERSON>": "<PERSON><PERSON>",
  "<PERSON>": "<PERSON>",
  "<PERSON>": "Mr. <PERSON>",
  "<PERSON><PERSON>": "<PERSON><PERSON><PERSON>",
  "<PERSON>": "<PERSON>",
  "<PERSON>": "<PERSON><PERSON><PERSON>",
  "<PERSON>alia Vito<PERSON>": "<PERSON><PERSON>",
  "<PERSON>": "<PERSON>",
  "<PERSON>": "<PERSON>",
  "<PERSON>": "<PERSON>",
  "<PERSON>": "<PERSON>",

  // You can add more mappings below, keys are matched exactly.
  // "Short Name": "Canonical Name",
};

/**
 * Normalize a name using the overrides table. Returns the mapped name
 * if present, otherwise returns the original name.
 */
export function normalizeName(name: string | null | undefined): string {
  if (!name) return '';
  return nameOverrides[name] ?? name;
}

export default nameOverrides;
