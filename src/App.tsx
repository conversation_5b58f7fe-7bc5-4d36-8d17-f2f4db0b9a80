import React, { useState } from 'react';
import { Navigation } from './components/Navigation';
import { OverviewView } from './components/views/OverviewView';
import { ClassesView } from './components/views/ClassesView';
import { StudentsView } from './components/views/StudentsView';
import { TeachersView } from './components/views/TeachersView';
import { SearchView } from './components/views/SearchView';
import { OverlapView } from './components/views/OverlapView';
import { useClassData } from './hooks/useClassData';
import { SettingsProvider } from './contexts/SettingsContext';
import { ErrorBoundary } from './components/ErrorBoundary';
import { SettingsOverlay } from './components/SettingsOverlay';
import { DataWrapper } from './components/DataWrapper';
import { GraduationCap, Settings } from 'lucide-react';

function App() {
  const [activeView, setActiveView] = useState('overview');
  const [settingsOpen, setSettingsOpen] = useState(false);
  const data = useClassData();

  const renderView = () => {
    switch (activeView) {
      case 'overview':
        return <OverviewView data={data} />;
      case 'classes':
        return <ClassesView data={data} />;
      case 'students':
        return <StudentsView data={data} />;
      case 'teachers':
        return <TeachersView data={data} />;
      case 'search':
        return <SearchView data={data} />;
      case 'overlap':
        return <OverlapView data={data} />;
      default:
        return <OverviewView data={data} />;
    }
  };

  return (
    <SettingsProvider>
      <ErrorBoundary>
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors">
          <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-2 rounded-lg">
                    <GraduationCap className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                      LJCDS Class Viewer
                    </h1>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Comprehensive class list and student viewing system
                    </p>
                  </div>
                </div>
                <button
                  onClick={() => setSettingsOpen(true)}
                  className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                  title="Settings"
                >
                  <Settings className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                </button>
              </div>
            </div>
          </header>

          <Navigation activeView={activeView} onViewChange={setActiveView} />

          <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <ErrorBoundary>
              <DataWrapper data={data}>
                {renderView()}
              </DataWrapper>
            </ErrorBoundary>
          </main>

          <SettingsOverlay
            isOpen={settingsOpen}
            onClose={() => setSettingsOpen(false)}
          />
        </div>
      </ErrorBoundary>
    </SettingsProvider>
  );
}

export default App;
