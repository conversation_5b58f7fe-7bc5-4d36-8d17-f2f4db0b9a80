export interface Student {
  name: string;
  email: string | null;
  gradYear: string;
}

export interface Faculty {
  name: string;
  email: string | null;
}

export interface ClassInfo {
  name: string;
  block: string;
  students: Student[];
  teacher: Faculty;
}

export interface ClassData {
  [teacherEmail: string]: {
    [className: string]: {
      block: string;
      students: Array<{
        name: string;
        email: string | null;
      }>;
    };
  };
}

export interface ProcessedData {
  classes: ClassInfo[];
  students: Student[];
  faculty: Faculty[];
  studentClassMap: Map<string, ClassInfo[]>;
  teacherClassMap: Map<string, ClassInfo[]>;
}