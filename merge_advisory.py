import json

def merge_advisory_into_classlist():
    # Load the data files
    with open('src/data/flex_4.json', 'r') as f:
        advisory_data = json.load(f)

    with open('src/data/upcoming_classlist_blocks.json', 'r') as f:
        classlist_data = json.load(f)

    # Merge advisory data into classlist
    for teacher_email, classes in advisory_data.items():
        # Initialize teacher if not exists
        if teacher_email not in classlist_data:
            classlist_data[teacher_email] = {}

        # Add each advisory class
        for class_name, class_info in classes.items():
            classlist_data[teacher_email][class_name] = class_info

    # Write back to the file
    with open('src/data/upcoming_classlist_blocks_3.json', 'w') as f:
        json.dump(classlist_data, f, indent=2)

    print("Advisory classes merged successfully!")

if __name__ == "__main__":
    merge_advisory_into_classlist()
