<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title data-default>LJCDS Class Viewer</title>
    <script>
      // Prevent flash of unstyled content by applying dark mode immediately
      (function() {
        try {
          const settings = JSON.parse(localStorage.getItem('ljcds-settings') || '{}');
          if (settings.darkMode) {
            document.documentElement.classList.add('dark');
          }
        } catch (e) {
          // Ignore errors
        }
      })();
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
